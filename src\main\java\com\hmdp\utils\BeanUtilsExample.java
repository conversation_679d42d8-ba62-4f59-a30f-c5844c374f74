package com.hmdp.utils;

import cn.hutool.core.bean.BeanUtil;


public class BeanUtilsExample {
    public static void main(String[] args) {
        SourceBean source = new SourceBean("<PERSON>", 30);
        TargetBean target = new TargetBean();

        try {
            BeanUtil.copyProperties(source, target);
            System.out.println("Target Name: " + target.getName());
            System.out.println("Target Age: " + target.getAge());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

class SourceBean {
    private String name;
    private int age;

    // Constructors, getters and setters
    public SourceBean(String name, int age) {
        this.name = name;
        this.age = age;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }
}

class TargetBean {
    private String name;
    private int age;

    // Getters and setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }
}
